import request from '@/utils/request'

// 查询设备库存列表
export function listFacilityStorage(query) {
  return request({
    url: '/biz/facilityStorage/list',
    method: 'get',
    params: query
  })
}

// 查询设备库存详细
export function getFacilityStorage(id) {
  return request({
    url: '/biz/facilityStorage/' + id,
    method: 'get'
  })
}

// 新增设备库存
export function addFacilityStorage(data) {
  return request({
    url: '/biz/facilityStorage',
    method: 'post',
    data: data
  })
}

// 修改设备库存
export function updateFacilityStorage(data) {
  return request({
    url: '/biz/facilityStorage',
    method: 'put',
    data: data
  })
}

// 删除设备库存
export function delFacilityStorage(id) {
  return request({
    url: '/biz/facilityStorage/' + id,
    method: 'delete'
  })
}
