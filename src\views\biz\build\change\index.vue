<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="施工计划" prop="buildPlanId">
        <el-select v-model="queryParams.buildPlanId" clearable placeholder="请选择">
          <el-option
            v-for="dict in buildPlanOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['biz:planChange:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:planChange:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:planChange:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="planChangeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="施工计划" align="center" prop="buildPlanName" />
      <el-table-column label="变更事由" align="center" prop="changeReason" />
      <el-table-column label="变更单" align="center" prop="ossId" />
      <el-table-column label="变更状态" align="center" prop="changeStatus" />
      <el-table-column label="变更时间" align="center" prop="changeTime" width="180"/>
      <el-table-column label="项目id" align="center" prop="projectId" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['biz:planChange:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:planChange:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改计划变更对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="施工计划" prop="buildPlanId">
          <el-select v-model="form.buildPlanId" placeholder="请选择">
            <el-option
              v-for="dict in buildPlanOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="变更事由" prop="changeReason">
          <el-input v-model="form.changeReason" placeholder="请输入变更事由" />
        </el-form-item>
        <el-form-item label="变更单" prop="ossId">
          <el-input v-model="form.ossId" placeholder="请输入变更单" />
        </el-form-item>
        <el-form-item label="变更状态" prop="changeStatus">
          <el-input v-model="form.changeStatus" placeholder="变更状态" />
        </el-form-item>
        <el-form-item label="变更时间" prop="changeTime">
          <el-date-picker clearable
                          v-model="form.changeTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          placeholder="请选择变更时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPlanChange, getPlanChange, delPlanChange, addPlanChange, updatePlanChange } from "@/api/biz/planChange";
import {buildPlanOption} from "@/api/biz/buildPlan";

export default {
  name: "PlanChange",
  data() {
    return {
      // 项目id
      projectId: localStorage.getItem('projectId'),
      // 施工计划选项
      buildPlanOptions: [],
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划变更表格数据
      planChangeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        buildPlanId: undefined,
        changeReason: undefined,
        ossId: undefined,
        changeStatus: undefined,
        changeTime: undefined,
        projectId: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        buildPlanId: [
          { required: true, message: "施工计划不能为空", trigger: "blur" }
        ],
        changeReason: [
          { required: true, message: "变更事由不能为空", trigger: "blur" }
        ],
        ossId: [
          { required: true, message: "变更单不能为空", trigger: "blur" }
        ],
        changeStatus: [
          { required: true, message: "变更状态不能为空", trigger: "change" }
        ],
        changeTime: [
          { required: true, message: "变更时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getBuildPlanOption();
  },
  methods: {
    // 施工计划选项
    getBuildPlanOption() {
      buildPlanOption({
        projectId: this.projectId
      }).then(res => {
        this.buildPlanOptions = res.data;
      })
    },
    /** 查询计划变更列表 */
    getList() {
      this.loading = true;
      this.queryParams.projectId = this.projectId
      listPlanChange(this.queryParams).then(response => {
        this.planChangeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        buildPlanId: undefined,
        changeReason: undefined,
        ossId: undefined,
        changeStatus: undefined,
        changeTime: undefined,
        projectId: undefined,
        delFlag: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加计划变更";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getPlanChange(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改计划变更";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          this.form.projectId = this.projectId
          if (this.form.id != null) {
            updatePlanChange(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addPlanChange(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除计划变更编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delPlanChange(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>
