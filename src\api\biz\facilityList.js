import request from '@/utils/request'

// 查询设备清单列表
export function listFacilityList(query) {
  return request({
    url: '/biz/facilityList/list',
    method: 'get',
    params: query
  })
}

// 查询设备清单详细
export function getFacilityList(id) {
  return request({
    url: '/biz/facilityList/' + id,
    method: 'get'
  })
}

// 新增设备清单
export function addFacilityList(data) {
  return request({
    url: '/biz/facilityList',
    method: 'post',
    data: data
  })
}

// 修改设备清单
export function updateFacilityList(data) {
  return request({
    url: '/biz/facilityList',
    method: 'put',
    data: data
  })
}

// 删除设备清单
export function delFacilityList(id) {
  return request({
    url: '/biz/facilityList/' + id,
    method: 'delete'
  })
}

// 设备清单选项
export function facilityOption() {
  return request({
    url: '/biz/facilityList/option',
    method: 'get'
  })
}
