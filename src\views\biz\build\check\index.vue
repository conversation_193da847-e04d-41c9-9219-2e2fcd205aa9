<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="验收人" prop="acceptor">
        <el-input
          v-model="queryParams.acceptor"
          placeholder="请输入验收人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['biz:projectCheck:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:projectCheck:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:projectCheck:remove']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectCheckList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="验收报告" align="center" prop="reportOssId"/>
      <el-table-column label="验收资料" align="center" prop="dataOssId"/>
      <el-table-column label="验收人" align="center" prop="acceptor"/>
      <el-table-column label="验收时间" align="center" prop="receiveTime" width="180"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['biz:projectCheck:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:projectCheck:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目验收对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目" prop="projectId">
          <el-select v-model="form.projectId" placeholder="请选择">
            <el-option
              v-for="dict in projectOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="验收报告" prop="reportOssId">
          <el-input v-model="form.reportOssId" placeholder="请输入验收报告"/>
        </el-form-item>
        <el-form-item label="验收资料" prop="dataOssId">
          <el-input v-model="form.dataOssId" placeholder="请输入验收资料"/>
        </el-form-item>
        <el-form-item label="验收人" prop="acceptor">
          <el-input v-model="form.acceptor" placeholder="请输入验收人"/>
        </el-form-item>
        <el-form-item label="验收时间" prop="receiveTime">
          <el-date-picker clearable
                          v-model="form.receiveTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          placeholder="请选择验收时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProjectCheck,
  getProjectCheck,
  delProjectCheck,
  addProjectCheck,
  updateProjectCheck
} from "@/api/biz/projectCheck";
import {projectOption} from "@/api/biz/project";

export default {
  name: "ProjectCheck",
  data() {
    return {
      // 项目id
      projectId: localStorage.getItem('projectId'),
      // 项目选项
      projectOptions: [],
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目验收表格数据
      projectCheckList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportOssId: undefined,
        dataOssId: undefined,
        acceptor: undefined,
        receiveTime: undefined,
        projectId: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        reportOssId: [
          {required: true, message: "验收报告不能为空", trigger: "blur"}
        ],
        dataOssId: [
          {required: true, message: "验收资料不能为空", trigger: "blur"}
        ],
        acceptor: [
          {required: true, message: "验收人不能为空", trigger: "blur"}
        ],
        receiveTime: [
          {required: true, message: "验收时间不能为空", trigger: "blur"}
        ],
        projectId: [
          {required: true, message: "项目不能为空", trigger: "blur"}
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getProjectOption();
  },
  methods: {
    // 项目选项
    getProjectOption() {
      projectOption({
        id: this.projectId
      }).then(res => {
        this.projectOptions = res.data;
      })
    },
    /** 查询项目验收列表 */
    getList() {
      this.loading = true;
      this.queryParams.projectId = this.projectId;
      listProjectCheck(this.queryParams).then(response => {
        this.projectCheckList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        reportOssId: undefined,
        dataOssId: undefined,
        acceptor: undefined,
        receiveTime: undefined,
        projectId: undefined,
        delFlag: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目验收";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getProjectCheck(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改项目验收";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          this.form.projectId = this.projectId;
          if (this.form.id != null) {
            updateProjectCheck(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addProjectCheck(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除项目验收编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delProjectCheck(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>
